// Node.js server untuk menjalankan chess engine via WebSocket
const WebSocket = require('ws');
const { spawn } = require('child_process');
const express = require('express');
const path = require('path');

const app = express();
const PORT = 3000;

// Serve static files
app.use(express.static(path.join(__dirname)));

// Serve main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'chess_server.html'));
});

// WebSocket server
const wss = new WebSocket.Server({ port: 8080 });

class ChessEngineServer {
    constructor(ws) {
        this.ws = ws;
        this.engine = null;
        this.isReady = false;
        this.initializeEngine();
        this.setupWebSocket();
    }

    initializeEngine() {
        // Spawn chess engine process
        this.engine = spawn('./chess_engine.exe', [], {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        this.engine.stdout.on('data', (data) => {
            const output = data.toString().trim();
            console.log('Engine output:', output);

            if (output.includes('uciok')) {
                this.isReady = true;
                this.sendToClient({ type: 'engine_ready' });
            } else if (output.includes('bestmove')) {
                const move = this.parseBestMove(output);
                this.sendToClient({
                    type: 'engine_move',
                    move: move,
                    output: output
                });
            } else if (output.includes('info')) {
                this.sendToClient({
                    type: 'engine_info',
                    info: output
                });
            }
        });

        this.engine.stderr.on('data', (data) => {
            console.error('Engine error:', data.toString());
        });

        this.engine.on('close', (code) => {
            console.log('Engine process closed with code:', code);
        });

        // Initialize engine
        this.sendToEngine('uci');
    }

    setupWebSocket() {
        this.ws.on('message', (message) => {
            try {
                const data = JSON.parse(message);
                this.handleClientMessage(data);
            } catch (error) {
                console.error('Error parsing message:', error);
            }
        });

        this.ws.on('close', () => {
            if (this.engine) {
                this.engine.kill();
            }
        });
    }

    handleClientMessage(data) {
        switch (data.type) {
            case 'new_game':
                this.sendToEngine('ucinewgame');
                this.sendToEngine('isready');
                break;

            case 'position':
                this.sendToEngine(`position ${data.position}`);
                break;

            case 'go':
                const goCommand = this.buildGoCommand(data.params);
                this.sendToEngine(goCommand);
                break;

            case 'stop':
                this.sendToEngine('stop');
                break;

            case 'set_option':
                this.sendToEngine(`setoption name ${data.name} value ${data.value}`);
                break;

            case 'quit':
                this.sendToEngine('quit');
                break;
        }
    }

    buildGoCommand(params) {
        let command = 'go';

        if (params.depth) command += ` depth ${params.depth}`;
        if (params.movetime) command += ` movetime ${params.movetime}`;
        if (params.wtime) command += ` wtime ${params.wtime}`;
        if (params.btime) command += ` btime ${params.btime}`;
        if (params.infinite) command += ' infinite';

        return command;
    }

    parseBestMove(output) {
        const match = output.match(/bestmove (\w+)/);
        return match ? match[1] : null;
    }

    sendToEngine(command) {
        if (this.engine && this.engine.stdin.writable) {
            console.log('Sending to engine:', command);
            this.engine.stdin.write(command + '\n');
        }
    }

    sendToClient(data) {
        if (this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        }
    }
}

// Handle WebSocket connections
wss.on('connection', (ws) => {
    console.log('New client connected');
    new ChessEngineServer(ws);
});

// Start HTTP server
app.listen(PORT, () => {
    console.log(`Chess server running on http://localhost:${PORT}`);
    console.log(`WebSocket server running on ws://localhost:8080`);
});

console.log('VibeChess Web Server');
console.log('===================');
console.log('Make sure chess_engine_uci.exe is in the same directory');
console.log('Install dependencies: npm install ws express');
console.log(`Open http://localhost:${PORT} in your browser`);
