/**
 * ENHANCED CHESS ENGINE EVALUATION
 *
 * This file implements Phase 1 improvements from the roadmap:
 * 1. Comprehensive Piece-Square Tables (PSTs) with tapered evaluation
 * 2. Enhanced piece protection analysis for tactical awareness
 * 3. Game phase recognition (middlegame vs endgame)
 * 4. Improved evaluation function structure
 *
 * Key improvements over basic engine:
 * - Uses professional-grade piece-square tables from ImprovedPieceSquareTables.h
 * - Analyzes piece safety (attacked/defended status)
 * - Implements tapered evaluation that smoothly transitions from middlegame to endgame
 * - Provides foundation for advanced search algorithms
 *
 * Target: Improve from ~450 ELO to stronger tactical play
 */

#include "ChessEngine.h"
#include "ImprovedPieceSquareTables.h"
#include "ZobristHash.h"
#include <algorithm>
#include <cmath>
#include <random>

// ===============================================================
// 1. PIECE PROTECTION & TACTICAL AWARENESS
// ===============================================================

/**
 * Enhanced position evaluation function with piece protection analysis
 *
 * This function evaluates a chess position from the perspective of a given color.
 * It considers multiple factors:
 * 1. Material value with game phase adjustments
 * 2. Piece-square table values (positional bonuses)
 * 3. Piece protection and tactical safety
 * 4. Game phase specific evaluation (middlegame vs endgame)
 *
 * @param board The chess board to evaluate
 * @param perspective The color from whose perspective to evaluate (positive = good for this color)
 * @return Evaluation score in centipawns (100 = 1 pawn advantage)
 */
int ChessEngine::evaluatePosition(const ChessBoard &board, Color perspective) const
{
    int score = 0;
    int gamePhase = calculateGamePhase(board);
    bool isEndgame = (gamePhase < 64); // Endgame when few pieces left

    // 1. MATERIAL EVALUATION with piece protection analysis
    // This is the core evaluation loop that examines every square on the board
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);
            if (piece)
            {
                int pieceValue = getPieceValue(piece->getType(), isEndgame);
                int positionValue = getPieceSquareValue(piece->getType(), pos, piece->getColor(), isEndgame, board);

                // PIECE PROTECTION ANALYSIS
                bool isAttacked = board.isSquareAttackedBy(pos, oppositeColor(piece->getColor()));
                bool isDefended = board.isSquareAttackedBy(pos, piece->getColor());

                // Calculate protection bonus/penalty
                int protectionScore = 0;
                if (isAttacked && !isDefended)
                {
                    // HANGING PIECE - Major penalty!
                    protectionScore = -pieceValue / 2; // Lose half the piece value
                }
                else if (isAttacked && isDefended)
                {
                    // Attacked but defended - small penalty for tension
                    protectionScore = -pieceValue / 10;
                }
                else if (isDefended && !isAttacked)
                {
                    // Well protected piece - small bonus
                    protectionScore = 5;
                }

                if (piece->getColor() == perspective)
                {
                    score += pieceValue + positionValue + protectionScore;
                }
                else
                {
                    score -= pieceValue + positionValue + protectionScore;
                }
            }
        }
    }

    // 2. TACTICAL PATTERNS
    score += evaluateTacticalPatterns(board, perspective);

    // 3. GAME PHASE SPECIFIC EVALUATION
    if (isEndgame)
    {
        score += evaluateEndgame(board, perspective);
    }
    else
    {
        score += evaluateMiddlegame(board, perspective);
    }

    // 4. KING SAFETY (crucial!)
    score += evaluateKingSafety(board, perspective);

    // 5. PAWN STRUCTURE
    score += evaluatePawnStructure(board, perspective);

    return score;
}

// ===============================================================
// 2. IMPROVED PIECE VALUES (game phase dependent)
// ===============================================================

int ChessEngine::getPieceValue(PieceType type, bool isEndgame) const
{
    switch (type)
    {
    case PieceType::PAWN:
        return isEndgame ? 120 : 100; // Pawns more valuable in endgame
    case PieceType::KNIGHT:
        return isEndgame ? 280 : 320; // Knights less valuable in endgame
    case PieceType::BISHOP:
        return isEndgame ? 350 : 330; // Bishops better in endgame
    case PieceType::ROOK:
        return isEndgame ? 550 : 500; // Rooks much better in endgame
    case PieceType::QUEEN:
        return 900; // Queen constant
    case PieceType::KING:
        return 2000; // REDUCED from 20,000! King can be active
    default:
        return 0;
    }
}

// ===============================================================
// 3. PIECE PROTECTION DETECTION
// ===============================================================

int ChessEngine::evaluateTacticalPatterns(const ChessBoard &board, Color color) const
{
    int score = 0;
    Color enemy = oppositeColor(color);

    // Check all our pieces for tactical opportunities/threats
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);

            if (piece && piece->getColor() == color)
            {
                // FORK DETECTION: Can this piece attack multiple enemy pieces?
                std::vector<Position> attacks = getAttackSquares(board, pos);
                int enemyPiecesAttacked = 0;
                int totalValueAttacked = 0;

                for (const Position &attackPos : attacks)
                {
                    const Piece *target = board.getPiece(attackPos);
                    if (target && target->getColor() == enemy)
                    {
                        enemyPiecesAttacked++;
                        totalValueAttacked += getPieceValue(target->getType(), false);
                    }
                }

                if (enemyPiecesAttacked >= 2)
                {
                    score += 50 + (totalValueAttacked / 10); // Fork bonus
                }

                // PIN DETECTION: Is this piece pinning an enemy piece?
                if (isPinningPiece(board, pos))
                {
                    score += 30; // Pin bonus
                }

                // DISCOVERY ATTACK: Can moving this piece create attacks?
                if (canCreateDiscoveredAttack(board, pos))
                {
                    score += 40; // Discovery bonus
                }
            }
        }
    }

    return score;
}

// ===============================================================
// 4. GAME PHASE RECOGNITION
// ===============================================================

/**
 * Calculate the current game phase based on remaining material
 *
 * Game phase is used for tapered evaluation - interpolating between
 * middlegame and endgame piece values and positional factors.
 * Higher values = more material = middlegame
 * Lower values = less material = endgame
 *
 * @param board The current board position
 * @return Phase value (256 = opening, 0 = pure endgame)
 */
int ChessEngine::calculateGamePhase(const ChessBoard &board) const
{
    int phase = 256; // Starting phase value

    // Subtract points for missing pieces (endgame when low)
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (piece)
            {
                switch (piece->getType())
                {
                case PieceType::KNIGHT:
                case PieceType::BISHOP:
                    phase -= 10;
                    break;
                case PieceType::ROOK:
                    phase -= 25;
                    break;
                case PieceType::QUEEN:
                    phase -= 90;
                    break;
                default:
                    break;
                }
            }
        }
    }

    return std::max(0, phase);
}

int ChessEngine::evaluateMiddlegame(const ChessBoard &board, Color color) const
{
    int score = 0;

    // 1. PIECE DEVELOPMENT
    score += evaluatePieceDevelopment(board, color);

    // 2. CENTER CONTROL
    score += evaluateCenterControl(board, color);

    // 3. PIECE COORDINATION
    score += evaluatePieceCoordination(board, color);

    return score;
}

int ChessEngine::evaluateEndgame(const ChessBoard &board, Color color) const
{
    int score = 0;

    // 1. KING ACTIVITY (very important in endgame)
    score += evaluateKingActivity(board, color);

    // 2. PASSED PAWNS (crucial in endgame)
    score += evaluatePassedPawns(board, color);

    // 3. PIECE COORDINATION for endgame
    score += evaluateEndgameCoordination(board, color);

    return score;
}

// ===============================================================
// 5. ENHANCED MOVE ORDERING (NO MORE RANDOM!)
// ===============================================================

std::vector<Move> ChessEngine::orderMoves(const std::vector<Move> &moves, const ChessGame &game) const
{
    std::vector<std::pair<Move, int>> scoredMoves;

    for (const Move &move : moves)
    {
        int score = 0;

        // 1. HASH MOVE (from transposition table) - highest priority
        if (isHashMove(move))
        {
            score += 10000;
        }

        // 2. CAPTURES with MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
        const Piece *captured = game.getBoard().getPiece(move.getTo());
        if (captured)
        {
            const Piece *attacker = game.getBoard().getPiece(move.getFrom());
            if (attacker)
            {
                // MVV-LVA: prioritize capturing valuable pieces with less valuable pieces
                int victimValue = getPieceValue(captured->getType(), false);
                int attackerValue = getPieceValue(attacker->getType(), false);

                // MVV-LVA score: victim value * 10 - attacker value
                // This ensures we try QxR before RxQ (both capture rook, but queen is more valuable attacker)
                int mvvLvaScore = victimValue * 10 - attackerValue;

                // Use SEE for more accurate evaluation
                int see = staticExchangeEvaluation(game, move);
                if (see > 0)
                {
                    score += 8000 + mvvLvaScore + see; // Good captures with MVV-LVA
                }
                else if (see == 0)
                {
                    score += 6000 + mvvLvaScore; // Equal trades with MVV-LVA
                }
                else if (see > -200) // Only slightly bad captures
                {
                    score += 4000 + mvvLvaScore + see; // Slightly bad captures
                }
                else
                {
                    score += 1000 + see; // Very bad captures (try last)
                }
            }
        }

        // 3. PROMOTIONS
        if (move.getType() == MoveType::PAWN_PROMOTION)
        {
            score += 4000;
            if (move.getPromotionPiece() == PieceType::QUEEN)
            {
                score += 500;
            }
        }

        // 4. KILLER MOVES (this will be updated in orderMovesAdvanced)
        // Note: This basic orderMoves doesn't have depth info, so we skip killer moves here

        // 5. CASTLING
        if (move.getType() == MoveType::CASTLE_KINGSIDE ||
            move.getType() == MoveType::CASTLE_QUEENSIDE)
        {
            score += 2500;
        }

        // 6. CHECKS
        if (givesCheck(game, move))
        {
            score += 1500;
        }

        // 7. PIECE TO BETTER SQUARE
        const Piece *movingPiece = game.getBoard().getPiece(move.getFrom());
        if (movingPiece)
        {
            int fromValue = getPieceSquareValue(movingPiece->getType(), move.getFrom(),
                                                movingPiece->getColor(), false, game.getBoard());
            int toValue = getPieceSquareValue(movingPiece->getType(), move.getTo(),
                                              movingPiece->getColor(), false, game.getBoard());
            score += (toValue - fromValue);
        }

        // 8. HISTORY HEURISTIC
        score += getHistoryScore(move);

        // NO MORE RANDOM NUMBERS!
        scoredMoves.push_back({move, score});
    }

    // Sort by score (highest first)
    std::sort(scoredMoves.begin(), scoredMoves.end(),
              [](const std::pair<Move, int> &a, const std::pair<Move, int> &b)
              {
                  return a.second > b.second;
              });

    std::vector<Move> orderedMoves;
    for (const auto &scoredMove : scoredMoves)
    {
        orderedMoves.push_back(scoredMove.first);
    }

    return orderedMoves;
}

/**
 * Advanced move ordering with depth-specific killer moves and hash move
 *
 * This is the enhanced version of move ordering that uses all available
 * heuristics including depth-specific killer moves and transposition table moves.
 *
 * @param moves List of moves to order
 * @param game Current game state
 * @param depth Current search depth (for killer moves)
 * @param ttMove Hash move from transposition table
 * @return Ordered list of moves (best moves first)
 */
std::vector<Move> ChessEngine::orderMovesAdvanced(const std::vector<Move> &moves, const ChessGame &game, int depth, const Move &ttMove) const
{
    std::vector<std::pair<Move, int>> scoredMoves;

    for (const Move &move : moves)
    {
        int score = 0;

        // 1. HASH MOVE (from transposition table) - highest priority
        if (ttMove.isValid() && move == ttMove)
        {
            score += 10000;
        }

        // 2. CAPTURES with MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
        const Piece *captured = game.getBoard().getPiece(move.getTo());
        if (captured)
        {
            const Piece *attacker = game.getBoard().getPiece(move.getFrom());
            if (attacker)
            {
                // MVV-LVA: prioritize capturing valuable pieces with less valuable pieces
                int victimValue = getPieceValue(captured->getType(), false);
                int attackerValue = getPieceValue(attacker->getType(), false);

                // MVV-LVA score: victim value * 10 - attacker value
                int mvvLvaScore = victimValue * 10 - attackerValue;

                // Use SEE for more accurate evaluation
                int see = staticExchangeEvaluation(game, move);
                if (see > 0)
                {
                    score += 8000 + mvvLvaScore + see; // Good captures with MVV-LVA
                }
                else if (see == 0)
                {
                    score += 6000 + mvvLvaScore; // Equal trades with MVV-LVA
                }
                else if (see > -200) // Only slightly bad captures
                {
                    score += 4000 + mvvLvaScore + see; // Slightly bad captures
                }
                else
                {
                    score += 1000 + see; // Very bad captures (try last)
                }
            }
        }

        // 3. PROMOTIONS
        if (move.getType() == MoveType::PAWN_PROMOTION)
        {
            score += 7000;
            if (move.getPromotionPiece() == PieceType::QUEEN)
            {
                score += 500;
            }
        }

        // 4. KILLER MOVES (depth-specific)
        if (isKillerMove(move, depth))
        {
            score += 5000;
        }

        // 5. CASTLING
        if (move.getType() == MoveType::CASTLE_KINGSIDE ||
            move.getType() == MoveType::CASTLE_QUEENSIDE)
        {
            score += 3000;
        }

        // 6. CHECKS
        if (givesCheck(game, move))
        {
            score += 2000;
        }

        // 7. PIECE TO BETTER SQUARE (piece-square table improvement)
        const Piece *movingPiece = game.getBoard().getPiece(move.getFrom());
        if (movingPiece)
        {
            int fromValue = getPieceSquareValue(movingPiece->getType(), move.getFrom(),
                                                movingPiece->getColor(), false, game.getBoard());
            int toValue = getPieceSquareValue(movingPiece->getType(), move.getTo(),
                                              movingPiece->getColor(), false, game.getBoard());
            score += (toValue - fromValue);
        }

        // 8. HISTORY HEURISTIC
        score += getHistoryScore(move);

        scoredMoves.push_back({move, score});
    }

    // Sort by score (highest first)
    std::sort(scoredMoves.begin(), scoredMoves.end(),
              [](const std::pair<Move, int> &a, const std::pair<Move, int> &b)
              {
                  return a.second > b.second;
              });

    std::vector<Move> orderedMoves;
    for (const auto &scoredMove : scoredMoves)
    {
        orderedMoves.push_back(scoredMove.first);
    }

    return orderedMoves;
}

// ===============================================================
// CONSTRUCTOR AND MAIN ENGINE FUNCTIONS
// ===============================================================

ChessEngine::ChessEngine(Color color, int depth, int threads)
    : maxDepth_(depth), engineColor_(color), searchDepth_(0), searchInProgress_(false),
      quietMode_(false), totalNodesSearched_(0), totalTbHits_(0),
      nodesSearched_(0), ttHits_(0), nullMoveCutoffs_(0), lmrReductions_(0),
      multiCutPrunings_(0), tbHits_(0), threadPool_(nullptr), stopSearch_(false),
      maxSearchTimeMs_(0), timeUp_(false)
{
    // Initialize killer moves and history table
    for (int i = 0; i < 64; ++i)
    {
        for (int j = 0; j < 2; ++j)
        {
            killerMoves_[i][j] = Move();
        }
        for (int j = 0; j < 64; ++j)
        {
            historyTable_[i][j] = 0;
        }
        principalVariation_[i] = Move();
    }

    // Initialize thread pool if threads > 1
    if (threads > 1)
    {
        threadPool_ = new ThreadPool(threads);
    }

    // Load opening book - try Perfect 2023 first, fallback to hardcoded
    std::string bookPath = "Book_Opening_Perfect_2023/Perfect_2023/BIN/Perfect2023.bin";
    if (!openingBook_.loadFromFile(bookPath))
    {
        std::cout << "Failed to load Perfect 2023 opening book, using hardcoded openings." << std::endl;
        openingBook_.loadFromFile(""); // Load hardcoded openings
    }

    // Also try to load the improved opening book
    improvedOpeningBook_.loadFromFile(bookPath);
}

Move ChessEngine::getBestMove(const ChessGame &game)
{
    return getBestMoveWithTime(game, 5000); // Default 5 seconds
}

Move ChessEngine::getBestMoveWithTime(const ChessGame &game, int timeMs)
{
    maxSearchTimeMs_ = timeMs;
    searchStartTime_ = std::chrono::steady_clock::now();
    timeUp_ = false;
    stopSearch_ = false;

    // Check opening book first
    if (openingBook_.hasBookMove(game))
    {
        Move bookMove = openingBook_.getBookMove(game);
        if (bookMove.isValid())
        {
            return bookMove;
        }
    }

    // Use iterative deepening
    ChessGame gameCopy = game;
    return iterativeDeepening(gameCopy, maxDepth_);
}

// ===============================================================
// SEARCH FUNCTIONS
// ===============================================================

Move ChessEngine::iterativeDeepening(ChessGame &game, int maxDepth)
{
    Move bestMove;
    int bestScore = -std::numeric_limits<int>::max();

    for (int depth = 1; depth <= maxDepth; ++depth)
    {
        // Check time limit
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - searchStartTime_).count();
        if (elapsed >= maxSearchTimeMs_)
        {
            break;
        }

        nodesSearched_ = 0;
        int score = principalVariationSearch(game, depth, -std::numeric_limits<int>::max(),
                                             std::numeric_limits<int>::max(),
                                             game.getCurrentPlayer() == engineColor_);

        if (!stopSearch_ && !timeUp_)
        {
            bestScore = score;
            if (!currentPV_.empty())
            {
                bestMove = currentPV_[0];
            }
        }

        // UCI info output
        if (!quietMode_ && uciInfoCallback_)
        {
            std::string info = "info depth " + std::to_string(depth) +
                               " score cp " + std::to_string(score) +
                               " nodes " + std::to_string(nodesSearched_.load());
            uciInfoCallback_(info);
        }
    }

    return bestMove;
}

// ===============================================================
// STUB IMPLEMENTATIONS FOR MISSING FUNCTIONS
// ===============================================================

// IMPROVED EVALUATION IMPLEMENTATIONS
int ChessEngine::evaluatePieceDevelopment(const ChessBoard &board, Color color) const
{
    int score = 0;

    // Check if knights and bishops are developed
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);
            if (piece && piece->getColor() == color)
            {
                if (piece->getType() == PieceType::KNIGHT)
                {
                    // Knights should not be on back rank in opening
                    int backRank = (color == Color::WHITE) ? 0 : 7;
                    if (pos.rank != backRank)
                    {
                        score += 15; // Development bonus
                    }
                }
                else if (piece->getType() == PieceType::BISHOP)
                {
                    // Bishops should not be on back rank in opening
                    int backRank = (color == Color::WHITE) ? 0 : 7;
                    if (pos.rank != backRank)
                    {
                        score += 12; // Development bonus
                    }
                }
            }
        }
    }

    return score;
}

int ChessEngine::evaluateCenterControl(const ChessBoard &board, Color color) const
{
    int score = 0;

    // Center squares: e4, e5, d4, d5
    Position centerSquares[] = {Position(3, 4), Position(4, 4), Position(3, 3), Position(4, 3)};

    for (const Position &center : centerSquares)
    {
        if (board.isSquareAttackedBy(center, color))
        {
            score += 10; // Control center square
        }

        const Piece *piece = board.getPiece(center);
        if (piece && piece->getColor() == color)
        {
            score += 20; // Occupy center square
        }
    }

    return score;
}

int ChessEngine::evaluateKingSafety(const ChessBoard &board, Color color) const
{
    int score = 0;
    Position kingPos = board.findKing(color);

    if (!kingPos.isValid())
        return -1000; // King missing!

    // Check if king is castled
    bool castled = false;
    if (color == Color::WHITE)
    {
        castled = (kingPos.file == 6 || kingPos.file == 2) && kingPos.rank == 0;
    }
    else
    {
        castled = (kingPos.file == 6 || kingPos.file == 2) && kingPos.rank == 7;
    }

    if (castled)
    {
        score += 30; // Castling bonus
    }

    // Penalty for exposed king
    int attackers = 0;
    Color enemy = oppositeColor(color);

    // Check squares around king
    for (int dr = -1; dr <= 1; ++dr)
    {
        for (int dc = -1; dc <= 1; ++dc)
        {
            Position adjacent(kingPos.rank + dr, kingPos.file + dc);
            if (board.isValidPosition(adjacent))
            {
                if (board.isSquareAttackedBy(adjacent, enemy))
                {
                    attackers++;
                }
            }
        }
    }

    score -= attackers * 5; // Penalty for each attacked square near king

    return score;
}

int ChessEngine::evaluatePawnStructure(const ChessBoard &board, Color color) const
{
    int score = 0;

    for (int file = 0; file < 8; ++file)
    {
        std::vector<int> pawnRanks;

        // Find all pawns in this file
        for (int rank = 0; rank < 8; ++rank)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (piece && piece->getType() == PieceType::PAWN && piece->getColor() == color)
            {
                pawnRanks.push_back(rank);
            }
        }

        // Doubled pawns penalty
        if (pawnRanks.size() > 1)
        {
            score -= 10 * (pawnRanks.size() - 1);
        }

        // Isolated pawn penalty
        if (pawnRanks.size() > 0)
        {
            bool hasNeighbor = false;

            // Check adjacent files for friendly pawns
            for (int adjFile = file - 1; adjFile <= file + 1; adjFile += 2)
            {
                if (adjFile >= 0 && adjFile < 8)
                {
                    for (int rank = 0; rank < 8; ++rank)
                    {
                        const Piece *piece = board.getPiece(Position(rank, adjFile));
                        if (piece && piece->getType() == PieceType::PAWN && piece->getColor() == color)
                        {
                            hasNeighbor = true;
                            break;
                        }
                    }
                }
                if (hasNeighbor)
                    break;
            }

            if (!hasNeighbor)
            {
                score -= 15; // Isolated pawn penalty
            }
        }
    }

    return score;
}

int ChessEngine::evaluatePieceCoordination(const ChessBoard &board, Color color) const
{
    // Simple coordination bonus for pieces supporting each other
    return 0; // Stub for now
}

int ChessEngine::evaluateKingActivity(const ChessBoard &board, Color color) const
{
    int score = 0;
    Position kingPos = board.findKing(color);

    if (!kingPos.isValid())
        return 0;

    // In endgame, active king is important
    // King should be centralized
    int centerDistance = std::abs(kingPos.rank - 3.5) + std::abs(kingPos.file - 3.5);
    score -= centerDistance * 2; // Closer to center is better

    return score;
}

int ChessEngine::evaluatePassedPawns(const ChessBoard &board, Color color) const
{
    int score = 0;

    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);

            if (piece && piece->getType() == PieceType::PAWN && piece->getColor() == color)
            {
                // Check if this is a passed pawn
                bool isPassed = true;
                Color enemy = oppositeColor(color);

                // Check for enemy pawns that can stop this pawn
                int direction = (color == Color::WHITE) ? 1 : -1;

                for (int checkRank = rank + direction;
                     checkRank >= 0 && checkRank < 8;
                     checkRank += direction)
                {
                    // Check same file and adjacent files
                    for (int checkFile = file - 1; checkFile <= file + 1; ++checkFile)
                    {
                        if (checkFile >= 0 && checkFile < 8)
                        {
                            const Piece *blocker = board.getPiece(Position(checkRank, checkFile));
                            if (blocker && blocker->getType() == PieceType::PAWN &&
                                blocker->getColor() == enemy)
                            {
                                isPassed = false;
                                break;
                            }
                        }
                    }
                    if (!isPassed)
                        break;
                }

                if (isPassed)
                {
                    // Bonus based on how advanced the pawn is
                    int advancement = (color == Color::WHITE) ? rank : (7 - rank);
                    score += 10 + advancement * 5;
                }
            }
        }
    }

    return score;
}

int ChessEngine::evaluateEndgameCoordination(const ChessBoard &board, Color color) const
{
    // Simple endgame coordination bonus
    return 0; // Stub for now
}

/**
 * Get piece-square table value for a piece at a given position
 *
 * Uses comprehensive piece-square tables from ImprovedPieceSquareTables.h
 * to evaluate positional bonuses/penalties. Implements tapered evaluation
 * that interpolates between middlegame and endgame values based on material.
 *
 * @param type The type of piece (PAWN, KNIGHT, BISHOP, ROOK, QUEEN, KING)
 * @param pos The position on the board (0-7, 0-7)
 * @param color The color of the piece (WHITE or BLACK)
 * @param isEndgame Whether we're in endgame phase
 * @param board The current board state (for game phase calculation)
 * @return Positional value in centipawns
 */
int ChessEngine::getPieceSquareValue(PieceType type, const Position &pos, Color color, bool isEndgame, const ChessBoard &board) const
{
    // Validate position bounds
    if (pos.rank < 0 || pos.rank > 7 || pos.file < 0 || pos.file > 7)
    {
        return 0;
    }

    int rank = pos.rank;
    int file = pos.file;

    // For black pieces, flip the rank to get correct perspective
    if (color == Color::BLACK)
    {
        rank = 7 - rank;
    }

    // Get values from piece-square tables based on game phase
    int mgValue = 0, egValue = 0;

    switch (type)
    {
    case PieceType::PAWN:
        mgValue = ImprovedPST::PAWN_MG[rank][file];
        egValue = ImprovedPST::PAWN_EG[rank][file];
        break;

    case PieceType::KNIGHT:
        mgValue = ImprovedPST::KNIGHT_MG[rank][file];
        egValue = ImprovedPST::KNIGHT_EG[rank][file];
        break;

    case PieceType::BISHOP:
        mgValue = ImprovedPST::BISHOP_MG[rank][file];
        egValue = ImprovedPST::BISHOP_EG[rank][file];
        break;

    case PieceType::ROOK:
        mgValue = ImprovedPST::ROOK_MG[rank][file];
        egValue = ImprovedPST::ROOK_EG[rank][file];
        break;

    case PieceType::QUEEN:
        mgValue = ImprovedPST::QUEEN_MG[rank][file];
        egValue = ImprovedPST::QUEEN_EG[rank][file];
        break;

    case PieceType::KING:
        mgValue = ImprovedPST::KING_MG[rank][file];
        egValue = ImprovedPST::KING_EG[rank][file];
        break;

    default:
        return 0;
    }

    // Tapered evaluation: interpolate between middlegame and endgame values
    // based on game phase (calculated in evaluatePosition)
    int gamePhase = calculateGamePhase(board);

    // Simple tapered evaluation - you can make this more sophisticated
    if (isEndgame)
    {
        return egValue;
    }
    else
    {
        // Blend middlegame and endgame values based on material remaining
        int phase = std::max(0, std::min(256, gamePhase));
        return (mgValue * phase + egValue * (256 - phase)) / 256;
    }
}

// Tactical pattern recognition stubs
bool ChessEngine::isPinningPiece(const ChessBoard &board, const Position &pos) const { return false; }
bool ChessEngine::canCreateDiscoveredAttack(const ChessBoard &board, const Position &pos) const { return false; }

std::vector<Position> ChessEngine::getAttackSquares(const ChessBoard &board, const Position &pos) const
{
    std::vector<Position> attacks;
    const Piece *piece = board.getPiece(pos);
    if (piece)
    {
        // Simple implementation - get all valid moves for the piece
        // This is a simplified version, real implementation would be more complex
        for (int rank = 0; rank < 8; ++rank)
        {
            for (int file = 0; file < 8; ++file)
            {
                Position target(rank, file);
                if (piece->isValidMove(pos, target, board))
                {
                    attacks.push_back(target);
                }
            }
        }
    }
    return attacks;
}

/**
 * Generate Zobrist hash for the current board position
 *
 * Uses proper Zobrist hashing which provides much better hash distribution
 * and includes all position-relevant information (pieces, castling, en passant, side to move).
 * This is crucial for an effective transposition table.
 *
 * @param board The chess board to hash
 * @return 64-bit Zobrist hash of the position
 */
uint64_t ChessEngine::generateHash(const ChessBoard &board) const
{
    // Calculate castling rights as integer
    int castlingRights = 0;
    if (board.canCastleKingside(Color::WHITE))
        castlingRights |= 1;
    if (board.canCastleQueenside(Color::WHITE))
        castlingRights |= 2;
    if (board.canCastleKingside(Color::BLACK))
        castlingRights |= 4;
    if (board.canCastleQueenside(Color::BLACK))
        castlingRights |= 8;

    // Get en passant file
    int enPassantFile = -1;
    Position enPassantTarget = board.getEnPassantTarget();
    if (enPassantTarget.isValid())
    {
        enPassantFile = enPassantTarget.file;
    }

    // Note: We don't have direct access to current player from board,
    // so we'll use WHITE as default. In a real implementation, this should
    // be passed as a parameter or stored in the board.
    Color sideToMove = Color::WHITE; // This should be passed as parameter

    // Use proper Zobrist hashing
    return ZobristHash::generateHash(board, sideToMove, castlingRights, enPassantFile);
}

/**
 * Generate Zobrist hash for a complete game state
 *
 * This version includes the current player information from the game state,
 * providing a more accurate hash for transposition table lookups.
 *
 * @param game The chess game to hash
 * @return 64-bit Zobrist hash of the position
 */
uint64_t ChessEngine::generateHashForGame(const ChessGame &game) const
{
    const ChessBoard &board = game.getBoard();
    Color currentPlayer = game.getCurrentPlayer();

    // Calculate castling rights as integer
    int castlingRights = 0;
    if (board.canCastleKingside(Color::WHITE))
        castlingRights |= 1;
    if (board.canCastleQueenside(Color::WHITE))
        castlingRights |= 2;
    if (board.canCastleKingside(Color::BLACK))
        castlingRights |= 4;
    if (board.canCastleQueenside(Color::BLACK))
        castlingRights |= 8;

    // Get en passant file
    int enPassantFile = -1;
    Position enPassantTarget = board.getEnPassantTarget();
    if (enPassantTarget.isValid())
    {
        enPassantFile = enPassantTarget.file;
    }

    // Use proper Zobrist hashing with correct side to move
    return ZobristHash::generateHash(board, currentPlayer, castlingRights, enPassantFile);
}

// Analysis string for UI
std::string ChessEngine::getAnalysisString() const
{
    std::string analysis = "Enhanced Chess Engine Analysis:\n";
    analysis += "- Piece Protection: ENABLED\n";
    analysis += "- Tactical Patterns: ENABLED\n";
    analysis += "- Game Phase Recognition: ENABLED\n";
    analysis += "- Deterministic Move Ordering: ENABLED\n";
    analysis += "- Enhanced Opening Book: ENABLED\n";
    analysis += "- Search Depth: " + std::to_string(maxDepth_) + "\n";
    analysis += "- Nodes Searched: " + std::to_string(nodesSearched_.load()) + "\n";
    analysis += "- TT Hits: " + std::to_string(ttHits_.load()) + "\n";
    return analysis;
}
