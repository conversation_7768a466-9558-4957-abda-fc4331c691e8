/**
 * CHESS ENGINE BENCHMARKING SYSTEM
 *
 * This file implements Phase 2.3 from the roadmap:
 * - Tactical puzzle test suite
 * - Performance benchmarking (nodes per second)
 * - Objective measurement of engine improvements
 *
 * Target: Provide metrics to measure Phase 2 improvements objectively
 */

#include "ChessEngine.h"
#include "ChessGame.h"
#include <iostream>
#include <chrono>
#include <vector>
#include <string>
#include <iomanip>

class ChessEngineBenchmark
{
private:
    ChessEngine *engine_;

    // Tactical test positions (FEN format)
    struct TacticalPuzzle
    {
        std::string fen;
        std::string bestMove;
        std::string description;
        int difficulty; // 1-5 scale
    };

    std::vector<TacticalPuzzle> tacticalPuzzles_;

public:
    ChessEngineBenchmark(ChessEngine *engine) : engine_(engine)
    {
        initializeTacticalPuzzles();
    }

    /**
     * Initialize tactical puzzle test suite
     * These are famous tactical positions that test engine strength
     */
    void initializeTacticalPuzzles()
    {
        // Basic tactical puzzles
        tacticalPuzzles_ = {
            // Pin tactics
            {
                "r1bqkb1r/pppp1ppp/2n2n2/4p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4",
                "Bxf7+",
                "Classic bishop sacrifice on f7",
                2},

            // Fork tactics
            {
                "rnbqkbnr/ppp1pppp/8/3p4/4P3/8/PPPP1PPP/RNBQKBNR w KQkq d6 0 2",
                "exd5",
                "Simple pawn capture",
                1},

            // Discovered attack
            {
                "r1bqk2r/pppp1ppp/2n2n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4",
                "d4",
                "Central pawn advance",
                2},

            // Back rank mate threat
            {
                "6k1/5ppp/8/8/8/8/5PPP/R5K1 w - - 0 1",
                "Ra8+",
                "Back rank mate",
                3},

            // Queen sacrifice
            {
                "r2qkb1r/ppp2ppp/2np1n2/2b1p3/2B1P3/3P1N2/PPP2PPP/RNBQK2R w KQkq - 0 4",
                "Bxf7+",
                "Queen sacrifice for mate",
                4},

            // Smothered mate pattern
            {
                "6k1/5ppp/8/8/8/8/5PPP/4R1K1 w - - 0 1",
                "Re8+",
                "Rook endgame technique",
                3}};
    }

    /**
     * Run tactical puzzle test suite
     * @param timePerPuzzle Time limit per puzzle in milliseconds
     * @return Percentage of puzzles solved correctly
     */
    double runTacticalTests(int timePerPuzzle = 5000)
    {
        std::cout << "\n=== TACTICAL PUZZLE TEST SUITE ===" << std::endl;
        std::cout << "Testing " << tacticalPuzzles_.size() << " tactical positions..." << std::endl;
        std::cout << "Time per puzzle: " << timePerPuzzle << "ms" << std::endl;
        std::cout << std::string(50, '-') << std::endl;

        int solved = 0;
        int total = tacticalPuzzles_.size();

        for (size_t i = 0; i < tacticalPuzzles_.size(); ++i)
        {
            const auto &puzzle = tacticalPuzzles_[i];

            std::cout << "Puzzle " << (i + 1) << "/" << total
                      << " (Difficulty: " << puzzle.difficulty << "/5)" << std::endl;
            std::cout << "Position: " << puzzle.fen << std::endl;
            std::cout << "Description: " << puzzle.description << std::endl;

            // Create game from FEN (simplified - in real implementation, parse FEN properly)
            ChessGame game;
            // Note: This is simplified. Real implementation would parse FEN string

            // Get engine's best move
            auto startTime = std::chrono::steady_clock::now();
            Move engineMove = engine_->getBestMoveWithTime(game, timePerPuzzle);
            auto endTime = std::chrono::steady_clock::now();

            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

            // Convert move to algebraic notation (simplified)
            std::string engineMoveStr = moveToAlgebraic(engineMove);

            bool correct = (engineMoveStr == puzzle.bestMove);
            if (correct)
                solved++;

            std::cout << "Expected: " << puzzle.bestMove << std::endl;
            std::cout << "Engine:   " << engineMoveStr << std::endl;
            std::cout << "Result:   " << (correct ? "✓ CORRECT" : "✗ INCORRECT") << std::endl;
            std::cout << "Time:     " << duration.count() << "ms" << std::endl;
            std::cout << std::string(30, '-') << std::endl;
        }

        double percentage = (double)solved / total * 100.0;
        std::cout << "\nTACTICAL TEST RESULTS:" << std::endl;
        std::cout << "Solved: " << solved << "/" << total << " ("
                  << std::fixed << std::setprecision(1) << percentage << "%)" << std::endl;

        return percentage;
    }

    /**
     * Run performance benchmark - measure nodes per second
     * @param depth Search depth for benchmark
     * @param positions Number of positions to test
     * @return Average nodes per second
     */
    uint64_t runPerformanceBenchmark(int depth = 5, int positions = 10)
    {
        std::cout << "\n=== PERFORMANCE BENCHMARK ===" << std::endl;
        std::cout << "Search depth: " << depth << std::endl;
        std::cout << "Test positions: " << positions << std::endl;
        std::cout << std::string(40, '-') << std::endl;

        uint64_t totalNodes = 0;
        auto totalStartTime = std::chrono::steady_clock::now();

        // Test different positions
        std::vector<std::string> testPositions = {
            "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",      // Starting position
            "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1",   // After 1.e4
            "rnbqkb1r/pppppppp/5n2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 1 2",  // After 1.e4 Nf6
            "rnbqkb1r/pppp1ppp/4pn2/8/4P3/8/PPPP1PPP/RNBQKBNR w KQkq - 0 3", // After 1.e4 Nf6 2.e5
        };

        for (int i = 0; i < positions && i < (int)testPositions.size(); ++i)
        {
            std::cout << "Position " << (i + 1) << ": ";

            ChessGame game;
            // Note: In real implementation, would set position from FEN

            // Clear engine statistics
            engine_->clearTT();

            auto startTime = std::chrono::steady_clock::now();
            engine_->getBestMoveWithTime(game, 3000); // 3 second search
            auto endTime = std::chrono::steady_clock::now();

            uint64_t nodes = engine_->getNodesSearched();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

            uint64_t nps = (duration.count() > 0) ? (nodes * 1000) / duration.count() : 0;

            totalNodes += nodes;

            std::cout << nodes << " nodes in " << duration.count()
                      << "ms (" << nps << " nps)" << std::endl;
        }

        auto totalEndTime = std::chrono::steady_clock::now();
        auto totalDuration = std::chrono::duration_cast<std::chrono::milliseconds>(totalEndTime - totalStartTime);

        uint64_t averageNps = (totalDuration.count() > 0) ? (totalNodes * 1000) / totalDuration.count() : 0;

        std::cout << std::string(40, '-') << std::endl;
        std::cout << "PERFORMANCE RESULTS:" << std::endl;
        std::cout << "Total nodes: " << totalNodes << std::endl;
        std::cout << "Total time:  " << totalDuration.count() << "ms" << std::endl;
        std::cout << "Average NPS: " << averageNps << " nodes/second" << std::endl;

        return averageNps;
    }

    /**
     * Run complete benchmark suite
     */
    void runCompleteBenchmark()
    {
        std::cout << "CHESS ENGINE BENCHMARK SUITE" << std::endl;
        std::cout << "Phase 2 Performance Testing" << std::endl;
        std::cout << std::string(60, '=') << std::endl;

        // Run tactical tests
        double tacticalScore = runTacticalTests(5000);

        // Run performance benchmark
        uint64_t nps = runPerformanceBenchmark(5, 4);

        // Summary
        std::cout << "\n"
                  << std::string(60, '=') << std::endl;
        std::cout << "BENCHMARK SUMMARY" << std::endl;
        std::cout << std::string(60, '=') << std::endl;
        std::cout << "Tactical strength: " << std::fixed << std::setprecision(1)
                  << tacticalScore << "%" << std::endl;
        std::cout << "Search speed:      " << nps << " nodes/second" << std::endl;

        // Estimate ELO based on tactical score (rough approximation)
        int estimatedElo = 400 + (int)(tacticalScore * 15); // Very rough estimate
        std::cout << "Estimated ELO:     ~" << estimatedElo << std::endl;

        std::cout << "\nPhase 2 improvements:" << std::endl;
        std::cout << "- Enhanced move ordering (MVV-LVA + killer moves)" << std::endl;
        std::cout << "- Zobrist hashing transposition table" << std::endl;
        std::cout << "- Improved search efficiency" << std::endl;
    }

private:
    /**
     * Convert move to algebraic notation (simplified implementation)
     */
    std::string moveToAlgebraic(const Move &move)
    {
        // Simplified conversion - real implementation would be more complex
        char fromFile = 'a' + move.getFrom().file;
        char fromRank = '1' + move.getFrom().rank;
        char toFile = 'a' + move.getTo().file;
        char toRank = '1' + move.getTo().rank;

        return std::string(1, fromFile) + std::string(1, fromRank) +
               std::string(1, toFile) + std::string(1, toRank);
    }

    /**
     * Run Phase 2 specific benchmark
     * Tests improvements from move ordering and transposition tables
     */
    void runPhase2Benchmark()
    {
        std::cout << "\n=== PHASE 2 SPECIFIC BENCHMARK ===" << std::endl;
        std::cout << "Testing Phase 2 improvements:" << std::endl;
        std::cout << "1. Enhanced move ordering (MVV-LVA + killer moves)" << std::endl;
        std::cout << "2. Zobrist hashing transposition table" << std::endl;
        std::cout << "3. Improved search efficiency" << std::endl;
        std::cout << std::string(50, '-') << std::endl;

        // Test move ordering effectiveness
        testMoveOrdering();

        // Test transposition table effectiveness
        testTranspositionTable();

        // Test overall search efficiency
        testSearchEfficiency();

        std::cout << "\nPhase 2 benchmark completed!" << std::endl;
    }

    /**
     * Test move ordering effectiveness
     */
    void testMoveOrdering()
    {
        std::cout << "\nTesting move ordering effectiveness..." << std::endl;

        // Create a position where move ordering matters
        ChessGame game;

        // Clear statistics
        engine_->clearTT();

        auto startTime = std::chrono::steady_clock::now();
        Move bestMove = engine_->getBestMoveWithTime(game, 3000);
        auto endTime = std::chrono::steady_clock::now();

        uint64_t nodes = engine_->getNodesSearched();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        std::cout << "Move ordering test results:" << std::endl;
        std::cout << "- Nodes searched: " << nodes << std::endl;
        std::cout << "- Time taken: " << duration.count() << "ms" << std::endl;
        std::cout << "- Search speed: " << (nodes * 1000 / duration.count()) << " nps" << std::endl;
        std::cout << "- Best move: " << moveToAlgebraic(bestMove) << std::endl;

        // Expected: With good move ordering, fewer nodes should be searched
        // for the same depth due to better alpha-beta pruning
    }

    /**
     * Test transposition table effectiveness
     */
    void testTranspositionTable()
    {
        std::cout << "\nTesting transposition table effectiveness..." << std::endl;

        ChessGame game;

        // First search - populate transposition table
        engine_->clearTT();
        auto startTime1 = std::chrono::steady_clock::now();
        engine_->getBestMoveWithTime(game, 2000);
        auto endTime1 = std::chrono::steady_clock::now();
        uint64_t nodes1 = engine_->getNodesSearched();
        uint64_t ttHits1 = engine_->getTTHits();

        // Second search - should benefit from transposition table
        auto startTime2 = std::chrono::steady_clock::now();
        engine_->getBestMoveWithTime(game, 2000);
        auto endTime2 = std::chrono::steady_clock::now();
        uint64_t nodes2 = engine_->getNodesSearched();
        uint64_t ttHits2 = engine_->getTTHits();

        auto duration1 = std::chrono::duration_cast<std::chrono::milliseconds>(endTime1 - startTime1);
        auto duration2 = std::chrono::duration_cast<std::chrono::milliseconds>(endTime2 - startTime2);

        std::cout << "Transposition table test results:" << std::endl;
        std::cout << "First search:  " << nodes1 << " nodes, " << duration1.count() << "ms" << std::endl;
        std::cout << "Second search: " << nodes2 << " nodes, " << duration2.count() << "ms" << std::endl;
        std::cout << "TT hits: " << ttHits2 << std::endl;

        if (duration2.count() < duration1.count())
        {
            double speedup = (double)duration1.count() / duration2.count();
            std::cout << "Speedup: " << speedup << "x faster on second search" << std::endl;
        }
    }

    /**
     * Test overall search efficiency
     */
    void testSearchEfficiency()
    {
        std::cout << "\nTesting overall search efficiency..." << std::endl;

        // Test with increasing depths to see scaling
        std::vector<int> depths = {3, 4, 5, 6};

        for (int depth : depths)
        {
            ChessGame game;
            engine_->setDepth(depth);
            engine_->clearTT();

            auto startTime = std::chrono::steady_clock::now();
            engine_->getBestMoveWithTime(game, 5000); // 5 second limit
            auto endTime = std::chrono::steady_clock::now();

            uint64_t nodes = engine_->getNodesSearched();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            uint64_t nps = (duration.count() > 0) ? (nodes * 1000) / duration.count() : 0;

            std::cout << "Depth " << depth << ": " << nodes << " nodes, "
                      << duration.count() << "ms (" << nps << " nps)" << std::endl;
        }

        std::cout << "\nExpected: Phase 2 improvements should show:" << std::endl;
        std::cout << "- Consistent high nodes/second across depths" << std::endl;
        std::cout << "- Better scaling with increased depth" << std::endl;
        std::cout << "- Efficient use of transposition table" << std::endl;
    }
};
