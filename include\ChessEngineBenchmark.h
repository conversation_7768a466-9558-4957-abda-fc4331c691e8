#pragma once

#include "ChessEngine.h"
#include <vector>
#include <string>

/**
 * Chess Engine Benchmarking System
 * 
 * Provides comprehensive testing and performance measurement tools
 * for evaluating chess engine improvements objectively.
 * 
 * Features:
 * - Tactical puzzle test suite
 * - Performance benchmarking (nodes per second)
 * - ELO estimation based on tactical strength
 * - Progress tracking for development phases
 */
class ChessEngineBenchmark
{
private:
    ChessEngine* engine_;
    
    // Tactical test positions (FEN format)
    struct TacticalPuzzle
    {
        std::string fen;
        std::string bestMove;
        std::string description;
        int difficulty; // 1-5 scale
    };
    
    std::vector<TacticalPuzzle> tacticalPuzzles_;
    
public:
    /**
     * Constructor
     * @param engine Pointer to the chess engine to benchmark
     */
    ChessEngineBenchmark(ChessEngine* engine);
    
    /**
     * Initialize tactical puzzle test suite
     * Loads famous tactical positions for testing engine strength
     */
    void initializeTacticalPuzzles();
    
    /**
     * Run tactical puzzle test suite
     * Tests the engine's ability to find tactical solutions
     * 
     * @param timePerPuzzle Time limit per puzzle in milliseconds
     * @return Percentage of puzzles solved correctly
     */
    double runTacticalTests(int timePerPuzzle = 5000);
    
    /**
     * Run performance benchmark
     * Measures search speed in nodes per second
     * 
     * @param depth Search depth for benchmark
     * @param positions Number of positions to test
     * @return Average nodes per second
     */
    uint64_t runPerformanceBenchmark(int depth = 5, int positions = 10);
    
    /**
     * Run complete benchmark suite
     * Executes both tactical and performance tests
     */
    void runCompleteBenchmark();
    
    /**
     * Run Phase 2 specific benchmark
     * Tests improvements from move ordering and transposition tables
     */
    void runPhase2Benchmark();
    
    /**
     * Compare performance before and after Phase 2 improvements
     * @param beforeNps Nodes per second before improvements
     * @param afterNps Nodes per second after improvements
     */
    void comparePhase2Performance(uint64_t beforeNps, uint64_t afterNps);
    
    /**
     * Test specific Phase 2 features
     */
    void testMoveOrdering();
    void testTranspositionTable();
    void testSearchEfficiency();
    
private:
    /**
     * Convert move to algebraic notation
     * @param move The move to convert
     * @return Algebraic notation string
     */
    std::string moveToAlgebraic(const Move& move);
    
    /**
     * Estimate ELO rating based on tactical score
     * @param tacticalPercentage Percentage of tactical puzzles solved
     * @return Estimated ELO rating
     */
    int estimateEloFromTactical(double tacticalPercentage);
    
    /**
     * Print benchmark header
     */
    void printBenchmarkHeader(const std::string& title);
    
    /**
     * Print benchmark results summary
     */
    void printResultsSummary(double tacticalScore, uint64_t nps, int estimatedElo);
};
