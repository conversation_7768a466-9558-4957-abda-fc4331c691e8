// CHESS ENGINE SEARCH IMPLEMENTATION
// Implementation of search functions for ChessEngine

#include "ChessEngine.h"
#include <algorithm>
#include <cmath>

/**
 * Principal Variation Search (PVS) - Advanced alpha-beta search algorithm
 *
 * This is the main search function that explores the game tree using
 * alpha-beta pruning with several enhancements:
 * - Time management (stops search when time limit reached)
 * - Transposition table lookup and storage
 * - Move ordering for better pruning
 * - Quiescence search at leaf nodes
 *
 * @param game Current game state
 * @param depth Remaining search depth
 * @param alpha Alpha bound (best score for maximizing player)
 * @param beta Beta bound (best score for minimizing player)
 * @param maximizingPlayer Whether this is a maximizing or minimizing node
 * @return Best evaluation score found at this depth
 */
int ChessEngine::principalVariationSearch(ChessGame &game, int depth, int alpha, int beta, bool maximizingPlayer)
{
    nodesSearched_++;

    // Check time limit
    if (nodesSearched_ % 1000 == 0)
    {
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - searchStartTime_).count();
        if (elapsed >= maxSearchTimeMs_)
        {
            timeUp_ = true;
            return 0;
        }
    }

    if (stopSearch_ || timeUp_)
    {
        return 0;
    }

    // Terminal node
    if (depth == 0)
    {
        return quiescenceSearch(game, alpha, beta, maximizingPlayer, 0);
    }

    // Check for checkmate/stalemate
    std::vector<Move> moves = game.getAllValidMoves();
    if (moves.empty())
    {
        if (game.getBoard().isInCheck(game.getCurrentPlayer()))
        {
            return maximizingPlayer ? -10000 + (maxDepth_ - depth) : 10000 - (maxDepth_ - depth);
        }
        else
        {
            return 0; // Stalemate
        }
    }

    // Transposition table lookup with proper Zobrist hash
    uint64_t hash = generateHashForGame(game);
    Move hashMove; // Hash move from transposition table
    auto ttEntry = transpositionTable_.find(hash);
    if (ttEntry != transpositionTable_.end())
    {
        if (ttEntry->second.depth >= depth)
        {
            ttHits_++;
            return ttEntry->second.score;
        }
        // Even if depth is insufficient, we can use the best move for ordering
        hashMove = ttEntry->second.bestMove;
    }

    // Order moves with advanced ordering (including hash move from TT)
    moves = orderMovesAdvanced(moves, game, depth, hashMove);

    int bestScore = maximizingPlayer ? -std::numeric_limits<int>::max() : std::numeric_limits<int>::max();
    Move bestMove;

    for (size_t i = 0; i < moves.size(); ++i)
    {
        const Move &move = moves[i];

        game.makeMove(move);
        int score = principalVariationSearch(game, depth - 1, alpha, beta, !maximizingPlayer);
        game.undoLastMove();

        if (maximizingPlayer)
        {
            if (score > bestScore)
            {
                bestScore = score;
                bestMove = move;
            }
            alpha = std::max(alpha, score);
        }
        else
        {
            if (score < bestScore)
            {
                bestScore = score;
                bestMove = move;
            }
            beta = std::min(beta, score);
        }

        if (beta <= alpha)
        {
            // Update killer moves
            updateKillerMoves(move, depth);
            break;
        }
    }

    // Store in transposition table
    TTEntry entry;
    entry.hash = hash;
    entry.depth = depth;
    entry.score = bestScore;
    entry.bestMove = bestMove;
    entry.flag = TTEntry::EXACT;
    transpositionTable_[hash] = entry;

    return bestScore;
}

/**
 * Enhanced Quiescence Search - searches "noisy" moves to avoid horizon effect
 *
 * This function extends the search beyond the normal depth limit by examining
 * tactical moves (captures, checks, promotions) to ensure we don't stop
 * searching in the middle of a tactical sequence. This greatly improves
 * tactical strength and reduces evaluation errors.
 *
 * Key improvements over basic quiescence search:
 * - Handles checks properly (must search all moves when in check)
 * - Includes promotions and checking moves, not just captures
 * - Uses delta pruning to skip hopeless captures
 * - Implements proper depth limiting to prevent infinite search
 *
 * @param game The current game state
 * @param alpha Alpha bound for alpha-beta pruning
 * @param beta Beta bound for alpha-beta pruning
 * @param maximizingPlayer Whether this is a maximizing or minimizing node
 * @param qsDepth Current quiescence search depth (for recursion limiting)
 * @return Best evaluation score found
 */
int ChessEngine::quiescenceSearch(ChessGame &game, int alpha, int beta, bool maximizingPlayer, int qsDepth)
{
    nodesSearched_++;

    // Prevent infinite quiescence search
    const int MAX_QS_DEPTH = 16;

    if (qsDepth >= MAX_QS_DEPTH)
    {
        return evaluatePosition(game.getBoard(), game.getCurrentPlayer()) * (maximizingPlayer ? 1 : -1);
    }

    // Check if we're in check - if so, we must search all moves, not just captures
    bool inCheck = game.getBoard().isInCheck(game.getCurrentPlayer());

    // Stand pat evaluation (only if not in check)
    int standPat = 0;
    if (!inCheck)
    {
        standPat = evaluatePosition(game.getBoard(), game.getCurrentPlayer());
        if (!maximizingPlayer)
            standPat = -standPat;

        if (maximizingPlayer)
        {
            if (standPat >= beta)
            {
                return beta;
            }
            if (standPat > alpha)
                alpha = standPat;
        }
        else
        {
            if (standPat <= alpha)
            {
                return alpha;
            }
            if (standPat < beta)
                beta = standPat;
        }
    }

    // Generate "noisy" moves: captures, checks, and promotions
    std::vector<Move> noisyMoves;
    std::vector<Move> allMoves = game.getAllValidMoves();

    for (const Move &move : allMoves)
    {
        bool isCapture = (game.getBoard().getPiece(move.getTo()) != nullptr);
        bool isPromotion = (move.getType() == MoveType::PAWN_PROMOTION);
        bool givesCheck = false;

        // Check if move gives check (expensive, so only do if not already a capture/promotion)
        if (!isCapture && !isPromotion && !inCheck)
        {
            givesCheck = this->givesCheck(game, move);
        }

        // Include move if it's a capture, promotion, gives check, or we're in check
        if (isCapture || isPromotion || givesCheck || inCheck)
        {
            noisyMoves.push_back(move);
        }
    }

    // If in check and no moves, it's checkmate
    if (inCheck && noisyMoves.empty())
    {
        return maximizingPlayer ? -20000 + qsDepth : 20000 - qsDepth; // Prefer shorter mates
    }

    // Order noisy moves for better alpha-beta pruning
    Move emptyMove; // No hash move in quiescence
    noisyMoves = orderMovesAdvanced(noisyMoves, game, qsDepth, emptyMove);

    for (const Move &move : noisyMoves)
    {
        // Delta pruning: skip moves that can't improve alpha significantly
        if (!inCheck && !givesCheck(game, move))
        {
            const Piece *captured = game.getBoard().getPiece(move.getTo());
            if (captured)
            {
                int captureValue = getPieceValue(captured->getType(), false);
                // If even capturing the piece + a margin can't improve alpha, skip
                if (standPat + captureValue + 200 < alpha)
                {
                    continue;
                }
            }
        }

        game.makeMove(move);
        int score = quiescenceSearch(game, alpha, beta, !maximizingPlayer, qsDepth + 1);
        game.undoLastMove();

        if (maximizingPlayer)
        {
            if (score >= beta)
            {
                return beta;
            }
            if (score > alpha)
                alpha = score;
        }
        else
        {
            if (score <= alpha)
            {
                return alpha;
            }
            if (score < beta)
                beta = score;
        }
    }

    return maximizingPlayer ? alpha : beta;
}

// ===============================================================
// HELPER FUNCTIONS
// ===============================================================

void ChessEngine::updateKillerMoves(const Move &move, int depth)
{
    if (depth >= 0 && depth < 64)
    {
        // Shift killer moves
        killerMoves_[depth][1] = killerMoves_[depth][0];
        killerMoves_[depth][0] = move;
    }
}

void ChessEngine::updateHistoryTable(const Move &move, int depth)
{
    if (move.getFrom().isValid() && move.getTo().isValid())
    {
        int from = move.getFrom().rank * 8 + move.getFrom().file;
        int to = move.getTo().rank * 8 + move.getTo().file;
        if (from >= 0 && from < 64 && to >= 0 && to < 64)
        {
            historyTable_[from][to] += depth * depth;
        }
    }
}

bool ChessEngine::isHashMove(const Move &move) const
{
    // Simple implementation - check if move is in current PV
    return !currentPV_.empty() && currentPV_[0] == move;
}

bool ChessEngine::isKillerMove(const Move &move, int depth) const
{
    // Check against killer moves for specific depth
    if (depth >= 0 && depth < 64)
    {
        return (killerMoves_[depth][0] == move || killerMoves_[depth][1] == move);
    }
    return false;
}

int ChessEngine::getHistoryScore(const Move &move) const
{
    if (move.getFrom().isValid() && move.getTo().isValid())
    {
        int from = move.getFrom().rank * 8 + move.getFrom().file;
        int to = move.getTo().rank * 8 + move.getTo().file;
        if (from >= 0 && from < 64 && to >= 0 && to < 64)
        {
            return historyTable_[from][to];
        }
    }
    return 0;
}

bool ChessEngine::givesCheck(const ChessGame &game, const Move &move) const
{
    ChessGame tempGame = game;
    tempGame.makeMove(move);
    return tempGame.getBoard().isInCheck(oppositeColor(game.getCurrentPlayer()));
}

/**
 * Static Exchange Evaluation (SEE) - evaluates the outcome of a capture sequence
 *
 * This function simulates the exchange of pieces on a square to determine
 * if a capture is profitable. It considers all pieces that can attack the
 * target square and simulates the sequence of captures.
 *
 * @param game Current game state
 * @param move The capture move to evaluate
 * @return Net material gain/loss from the exchange (positive = good)
 */
int ChessEngine::staticExchangeEvaluation(const ChessGame &game, const Move &move) const
{
    const Piece *captured = game.getBoard().getPiece(move.getTo());
    const Piece *attacker = game.getBoard().getPiece(move.getFrom());

    if (!captured || !attacker)
    {
        return 0;
    }

    // Get piece values
    int capturedValue = getPieceValue(captured->getType(), false);
    int attackerValue = getPieceValue(attacker->getType(), false);

    // Simple but improved SEE: consider if the attacker will be recaptured
    Position targetSquare = move.getTo();
    Color attackingColor = attacker->getColor();
    Color defendingColor = oppositeColor(attackingColor);

    // Check if the target square is defended
    bool isDefended = game.getBoard().isSquareAttackedBy(targetSquare, defendingColor);

    if (!isDefended)
    {
        // Free capture - just return the captured piece value
        return capturedValue;
    }

    // Find the least valuable defender
    int leastValuableDefender = 10000; // Start with a high value

    // Check all squares for defending pieces
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = game.getBoard().getPiece(pos);

            if (piece && piece->getColor() == defendingColor)
            {
                // Check if this piece can attack the target square
                std::vector<Position> moves = piece->getPossibleMoves(pos, game.getBoard());
                for (const Position &movePos : moves)
                {
                    if (movePos == targetSquare)
                    {
                        int defenderValue = getPieceValue(piece->getType(), false);
                        leastValuableDefender = std::min(leastValuableDefender, defenderValue);
                        break;
                    }
                }
            }
        }
    }

    if (leastValuableDefender == 10000)
    {
        // No defender found (shouldn't happen if isDefended was true)
        return capturedValue;
    }

    // Simple SEE calculation: gain - loss
    // We capture their piece, they recapture with least valuable defender
    int gain = capturedValue - attackerValue;

    // If the gain is positive even after losing our attacker, it's good
    return gain;
}
